<script lang="ts">
  import type { Common, Reactor } from "@commune/api";
  import type { GetAppropriateLocalization } from "$lib";

  import { getClient } from "$lib/acrpc";

  interface Props {
    post: Reactor.GetPostsOutput[number];
    locale: Common.WebsiteLocale;
    toLocaleHref: (href: string) => string;
    getAppropriateLocalization: GetAppropriateLocalization;
    currentUser?: any; // Current user for edit permissions
    onEditPost?: (post: Reactor.GetPostsOutput[number]) => void; // Edit callback
  }

  const { post, locale, toLocaleHref, getAppropriateLocalization, currentUser, onEditPost }: Props =
    $props();

  const i18n = {
    en: {
      usefulness: "Usefulness",
      editPost: "Edit Post",
      getPlural(n: number) {
        if (n === 1) return 0;

        return 1;
      },
      ratingTooltipText(rating: Reactor.Rating) {
        const likesWord = ["like", "likes"][this.getPlural(rating.likes % 10)];
        const dislikesWord = ["dislike", "dislikes"][this.getPlural(rating.dislikes % 10)];

        return `${rating.likes} ${likesWord}, ${rating.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n: number) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n: number) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n: number) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n: number) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now",
      },
    },
    ru: {
      usefulness: "Полезность",
      editPost: "Редактировать пост",
      getPlural(n: number) {
        if (n === 1) return 0;
        if (n >= 2 && n <= 4) return 1;

        return 2;
      },
      ratingTooltipText(rating: Reactor.Rating) {
        const likesWord = ["лайк", "лайка", "лайков"][this.getPlural(rating.likes % 10)];
        const dislikesWord = ["дизлайк", "дизлайка", "дизлайков"][
          this.getPlural(rating.dislikes % 10)
        ];

        return `${rating.likes} ${likesWord}, ${rating.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n: number) {
          const word = ["день", "дня", "дней"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        hours(n: number) {
          const word = ["час", "часа", "часов"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        minutes(n: number) {
          const word = ["минуту", "минуты", "минут"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        seconds(n: number) {
          const word = ["секунду", "секунды", "секунд"][i18n.ru.getPlural(n)];

          return `${n} ${word} назад`;
        },

        rightNow: "только что",
      },
    },
  };

  const { fetcher: api } = getClient();

  const t = $derived(i18n[locale]);

  let rating = $state(post.rating);
  let usefulness = $state(post.usefulness);

  const ratingValue = $derived(rating.likes - rating.dislikes);

  const ratingTooltipText = $derived(t.ratingTooltipText(rating));

  // Hover state for usefulness stars
  let hoveredStarIndex = $state<number | null>(null);
  // Focus state for usefulness stars (for mobile)
  let focusedStarIndex = $state<number | null>(null);

  const authorName = $derived(getAppropriateLocalization(post.author.name));
  const title = $derived(getAppropriateLocalization(post.title));
  const body = $derived(getAppropriateLocalization(post.body));
  const hubName = $derived(post.hub ? getAppropriateLocalization(post.hub.name) : null);
  const communityName = $derived(
    post.community ? getAppropriateLocalization(post.community.name) : null,
  );

  let copied = $state(false);
  let copiedTagId = $state<string | null>(null);

  // Check if current user can edit this post
  const canEditPost = $derived(
    currentUser && (currentUser.role === "admin" || currentUser.id === post.author.id),
  );

  // Format date for display
  function formatDate(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }

  function copyLink(evt: MouseEvent) {
    if (!evt.ctrlKey && !evt.shiftKey && !evt.altKey && !evt.metaKey) {
      evt.preventDefault();

      const link = `${window.location.origin}${toLocaleHref(`/reactor/${post.id}`)}`;
      navigator.clipboard.writeText(link);

      copied = true;

      setTimeout(() => (copied = false), 2000);
    }
  }

  async function like() {
    rating = await api.reactor.post.rating.post({
      id: post.id,
      type: "like",
    });
  }

  async function dislike() {
    rating = await api.reactor.post.rating.post({
      id: post.id,
      type: "dislike",
    });
  }

  async function rateUsefulness(value: number) {
    usefulness = await api.reactor.post.usefulness.post({
      id: post.id,
      value: value === usefulness?.value ? null : value,
    });

    // Clear focus states after rating to ensure correct visual feedback on mobile
    focusedStarIndex = null;
    hoveredStarIndex = null;
  }

  function copyTagId(tagId: string) {
    navigator.clipboard.writeText(tagId);

    copiedTagId = tagId;

    setTimeout(() => {
      if (copiedTagId === tagId) {
        copiedTagId = null;
      }
    }, 2000);
  }
</script>

<div class="post-card mb-4">
  <div class="card">
    <!-- Desktop Layout -->
    <div class="card-header d-none d-md-block">
      <!-- Hub and Community Header -->
      {#if post.hub || post.community}
        <div class="hub-community-header mb-3">
          <!-- Hub Row -->
          {#if post.hub}
            <div class="hub-row d-flex align-items-center mb-2">
              <div class="hub-image-container me-2">
                {#if post.hub.image}
                  <img src={`/images/${post.hub.image}`} alt={hubName || "Hub"} class="hub-image" />
                {:else}
                  <div class="hub-image-placeholder">
                    <i class="bi bi-collection text-muted"></i>
                  </div>
                {/if}
              </div>
              <a
                href={toLocaleHref(`/reactor/hubs/${post.hub.id}`)}
                class="hub-link text-decoration-none fw-medium"
              >
                {hubName}
              </a>
            </div>
          {/if}

          <!-- Community Row -->
          {#if post.community}
            <div class="community-row d-flex align-items-center">
              <div class="community-image-container me-2">
                {#if post.community.image}
                  <img
                    src={`/images/${post.community.image}`}
                    alt={communityName || "Community"}
                    class="community-image"
                  />
                {:else}
                  <div class="community-image-placeholder">
                    <i class="bi bi-people text-muted"></i>
                  </div>
                {/if}
              </div>
              <a
                href={toLocaleHref(`/reactor/communities/${post.community.id}`)}
                class="community-link text-decoration-none fw-medium"
              >
                {communityName}
              </a>
            </div>
          {/if}
        </div>
      {/if}

      <!-- Post Header with Author and Time -->
      <div class="post-header d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
          <!-- Rating -->
          <div class="rating-block d-flex align-items-center me-3">
            {#if ratingValue > 0}
              <span
                class="rating-value me-2 text-success"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>{ratingValue}</span
              >
            {:else if ratingValue < 0}
              <span
                class="rating-value me-2 text-danger"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>{ratingValue}</span
              >
            {:else}
              <span
                class="rating-value me-2"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title={ratingTooltipText}>0</span
              >
            {/if}
            <div class="rating-buttons">
              <button
                class={`btn btn-sm me-1 ${rating?.status === "like" ? "btn-success" : "btn-outline-success"}`}
                aria-label="Like"
                onclick={like}
              >
                <i class="bi bi-hand-thumbs-up"></i>
              </button>
              <button
                class={`btn btn-sm ${rating?.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`}
                aria-label="Dislike"
                onclick={dislike}
              >
                <i class="bi bi-hand-thumbs-down"></i>
              </button>
            </div>
          </div>

          <!-- Author info -->
          <div class="author-info d-flex align-items-center">
            {#if post.author.image}
              <img
                src={`/images/${post.author.image}`}
                alt={"avatar"}
                class="avatar rounded-circle me-2"
                width="32"
                height="32"
                style:object-fit="cover"
              />
            {:else}
              <div class="avatar rounded-circle me-2"></div>
            {/if}
            <div>
              <a
                href={toLocaleHref(`/users/${post.author.id}`)}
                class="author-name fw-bold"
                style:text-decoration="none"
              >
                {authorName ?? "Anonymous"}
              </a>
              <div class="post-time small text-muted" title={post.createdAt.toISOString()}>
                {formatDate(post.createdAt)}
              </div>
            </div>
          </div>
        </div>

        <div class="usefulness-block">
          <div class="d-flex flex-column align-items-start">
            {#if usefulness.count > 0}
              <span class="usefulness-label mb-1 text-muted small px-1">
                {t.usefulness} ({usefulness.count})
              </span>
            {:else}
              <span class="usefulness-label mb-1 text-muted small px-1">
                {t.usefulness}
              </span>
            {/if}

            <div
              role="group"
              aria-label="Usefulness rating"
              onmouseleave={() => (hoveredStarIndex = null)}
              onfocusout={() => (focusedStarIndex = null)}
              class="usefulness-stars-container"
              style="position: relative;"
            >
              {#if usefulness?.value && usefulness.value > 0}
                <div
                  class="user-rating-underline"
                  style="width: {(Math.ceil(usefulness.value / 2) * 100) / 5}%;"
                ></div>
              {/if}
              {#each Array(5) as _, i}
                <button
                  class="btn btn-sm p-0 px-1"
                  aria-label={`Rate usefulness ${i + 1}`}
                  onclick={() => rateUsefulness((i + 1) * 2)}
                  onmouseenter={() => (hoveredStarIndex = i)}
                  onfocus={() => (focusedStarIndex = i)}
                  onblur={() => (focusedStarIndex = null)}
                >
                  <i
                    class="bi bi-star{(
                      hoveredStarIndex !== null
                        ? i <= hoveredStarIndex
                        : focusedStarIndex !== null
                          ? i <= focusedStarIndex
                          : (i + 1) * 2 <= (usefulness?.totalValue ?? 0)
                    )
                      ? '-fill'
                      : ''} text-warning rating-star"
                  ></i>
                </button>
              {/each}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Layout -->
    <div class="card-header d-md-none mobile-header">
      <!-- 1. Hub and Community - Each on one row -->
      {#if post.hub || post.community}
        <div class="mobile-hub-community mb-3">
          <!-- Hub Row -->
          {#if post.hub}
            <div class="mobile-hub-row d-flex align-items-center mb-2">
              <div class="hub-image-container me-2">
                {#if post.hub.image}
                  <img src={`/images/${post.hub.image}`} alt={hubName || "Hub"} class="hub-image" />
                {:else}
                  <div class="hub-image-placeholder">
                    <i class="bi bi-collection text-muted"></i>
                  </div>
                {/if}
              </div>
              <a
                href={toLocaleHref(`/reactor/hubs/${post.hub.id}`)}
                class="hub-link text-decoration-none fw-medium"
              >
                {hubName}
              </a>
            </div>
          {/if}

          <!-- Community Row -->
          {#if post.community}
            <div class="mobile-community-row d-flex align-items-center mb-2">
              <div class="community-image-container me-2">
                {#if post.community.image}
                  <img
                    src={`/images/${post.community.image}`}
                    alt={communityName || "Community"}
                    class="community-image"
                  />
                {:else}
                  <div class="community-image-placeholder">
                    <i class="bi bi-people text-muted"></i>
                  </div>
                {/if}
              </div>
              <a
                href={toLocaleHref(`/reactor/communities/${post.community.id}`)}
                class="community-link text-decoration-none fw-medium"
              >
                {communityName}
              </a>
            </div>
          {/if}
        </div>
      {/if}

      <!-- 2. Rating and Usefulness - Both on one row -->
      <div class="mobile-rating-usefulness d-flex justify-content-between align-items-center mb-3">
        <!-- Rating -->
        <div class="mobile-rating-block d-flex align-items-center">
          {#if ratingValue > 0}
            <span
              class="rating-value me-2 text-success"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              title={ratingTooltipText}>{ratingValue}</span
            >
          {:else if ratingValue < 0}
            <span
              class="rating-value me-2 text-danger"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              title={ratingTooltipText}>{ratingValue}</span
            >
          {:else}
            <span
              class="rating-value me-2"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              title={ratingTooltipText}>0</span
            >
          {/if}
          <div class="rating-buttons">
            <button
              class={`btn btn-sm me-1 ${rating?.status === "like" ? "btn-success" : "btn-outline-success"}`}
              aria-label="Like"
              onclick={like}
            >
              <i class="bi bi-hand-thumbs-up"></i>
            </button>
            <button
              class={`btn btn-sm ${rating?.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`}
              aria-label="Dislike"
              onclick={dislike}
            >
              <i class="bi bi-hand-thumbs-down"></i>
            </button>
          </div>
        </div>

        <!-- Usefulness -->
        <div class="mobile-usefulness-block">
          <div class="d-flex flex-column align-items-end">
            {#if usefulness && usefulness.count > 0}
              <span class="usefulness-label mb-1 text-muted small">
                {t.usefulness} ({usefulness.count})
              </span>
            {:else}
              <span class="usefulness-label mb-1 text-muted small">
                {t.usefulness}
              </span>
            {/if}

            <div
              role="group"
              aria-label="Usefulness rating"
              onmouseleave={() => (hoveredStarIndex = null)}
              onfocusout={() => (focusedStarIndex = null)}
              class="usefulness-stars-container"
              style="position: relative;"
            >
              {#if usefulness?.value && usefulness.value > 0}
                <div
                  class="user-rating-underline"
                  style="width: {(Math.ceil(usefulness.value / 2) * 100) / 5}%;"
                ></div>
              {/if}
              {#each Array(5) as _, i}
                <button
                  class="btn btn-sm p-0 px-1"
                  aria-label={`Rate usefulness ${i + 1}`}
                  onclick={() => rateUsefulness((i + 1) * 2)}
                  onmouseenter={() => (hoveredStarIndex = i)}
                  onfocus={() => (focusedStarIndex = i)}
                  onblur={() => (focusedStarIndex = null)}
                >
                  <i
                    class="bi bi-star{(
                      hoveredStarIndex !== null
                        ? i <= hoveredStarIndex
                        : focusedStarIndex !== null
                          ? i <= focusedStarIndex
                          : (i + 1) * 2 <= (usefulness?.totalValue ?? 0)
                    )
                      ? '-fill'
                      : ''} text-warning rating-star"
                  ></i>
                </button>
              {/each}
            </div>
          </div>
        </div>
      </div>

      <!-- 3. Author and When Published -->
      <div class="mobile-author-time d-flex align-items-center">
        {#if post.author.image}
          <img
            src={`/images/${post.author.image}`}
            alt={"avatar"}
            class="avatar rounded-circle me-2"
            width="24"
            height="24"
            style:object-fit="cover"
          />
        {:else}
          <div class="avatar rounded-circle me-2" style="width: 24px; height: 24px;"></div>
        {/if}
        <div>
          <a
            href={toLocaleHref(`/users/${post.author.id}`)}
            class="author-name fw-medium"
            style:text-decoration="none"
          >
            {authorName ?? "Anonymous"}
          </a>
          <div class="post-time small text-muted" title={post.createdAt.toISOString()}>
            {formatDate(post.createdAt)}
          </div>
        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="d-flex justify-content-between align-items-start mb-2">
        <a href={toLocaleHref(`/reactor/${post.id}`)} class="post-title-link-wrapper flex-grow-1">
          <!-- 4. Title as h3 on mobile, h5 on desktop -->
          <h4 class="card-title mb-0">{title}</h4>
        </a>

        {#if canEditPost && onEditPost}
          <button
            type="button"
            class="btn btn-outline-secondary btn-sm ms-2"
            onclick={() => onEditPost?.(post)}
            title={t.editPost}
            aria-label={t.editPost}
          >
            <i class="bi bi-pencil"></i>
          </button>
        {/if}
      </div>

      <div class="card-text">{@html body}</div>

      <div class="tags mb-3">
        {#each [...post.tags] as tag}
          <button
            class={`badge me-1 ${copiedTagId === tag.id ? "bg-success text-white" : "bg-light text-secondary"}`}
            onclick={copyTagId.bind(null, tag.id)}
          >
            {getAppropriateLocalization(tag.name)}
          </button>
        {/each}
      </div>

      <div class="card-actions d-flex">
        <!-- <button class="btn btn-sm btn-outline-secondary me-2" aria-label="Save">
          <i class="bi bi-bookmark"></i>
        </button> -->
        <a href={toLocaleHref(`/reactor/${post.id}`)} target="_blank">
          <button
            class={`btn btn-sm ${copied ? "btn-success" : "btn-outline-secondary"}`}
            aria-label="Copy link"
            onclick={copyLink}
          >
            {#if copied}
              <i class="bi bi-check-circle"></i>
            {:else}
              <i class="bi bi-link-45deg"></i>
            {/if}
          </button>
        </a>
      </div>
    </div>
  </div>
</div>

<style>
  .post-card {
    transition: all 0.2s ease;
  }

  .rating-value {
    font-weight: bold;
    min-width: 30px;
    text-align: center;
  }

  .card-title {
    font-weight: 600;
  }

  .post-title-link-wrapper {
    display: block;
    color: black;
    text-decoration: none;
    width: 100%;
  }

  .post-title-link-wrapper:visited {
    color: gray;
  }

  .post-title-link-wrapper:visited .card-title {
    color: gray;
  }

  .post-title-link-wrapper:hover {
    color: black;
  }

  /* .post-title-link-wrapper:hover .card-title {
    color: black;
  } */

  .text-gradient-overlay {
    position: relative;
    height: 20px;
    margin-top: -20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
  }

  .tags {
    margin-top: 1rem;
  }

  .badge {
    font-weight: 500;
    padding: 0.5em 0.7em;
  }

  /* Hub and Community Header Styles */
  .hub-community-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.75rem;
  }

  .hub-image-container,
  .community-image-container {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    overflow: hidden;
  }

  .hub-image,
  .community-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .hub-image-placeholder,
  .community-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
  }

  .hub-image-placeholder i,
  .community-image-placeholder i {
    font-size: 14px;
  }

  .hub-link,
  .community-link {
    color: #495057;
    font-size: 0.9rem;
  }

  .hub-link:hover,
  .community-link:hover {
    color: #007bff;
    text-decoration: underline !important;
  }

  /* Mobile-specific styles */
  @media (max-width: 767.98px) {
    .card {
      margin-bottom: 1rem;
      border-radius: 0.5rem;
    }

    .card-body {
      padding: 1rem !important;
    }

    /* Mobile title styling - at least h3 sized */
    .mobile-title {
      font-size: 1.5rem;
      line-height: 1.3;
      font-weight: 600;
    }

    .card-text {
      font-size: 0.95rem;
      line-height: 1.4;
    }

    /* Improve readability on mobile */
    .post-content {
      font-size: 0.95rem;
    }

    /* Better spacing for mobile */
    .tags {
      margin-top: 0.75rem;
    }

    .badge {
      font-size: 0.8rem;
      padding: 0.4em 0.6em;
    }

    /* Mobile header layout */
    .mobile-header {
      padding: 0.5rem 1rem;
    }

    .mobile-hub-community {
      border-bottom: 1px solid #e9ecef;
      padding-bottom: 0;
      margin-bottom: 0 !important;
    }

    .mobile-rating-usefulness {
      border-bottom: 1px solid #e9ecef;
      padding-bottom: 0;
      margin-top: 0.5rem !important;
      margin-bottom: 0.5rem !important;
    }

    /* Touch-friendly rating buttons - reduced size */
    .mobile-rating-block .rating-buttons .btn {
      min-height: 32px;
      min-width: 32px;
      padding: 0.25rem;
    }

    .mobile-rating-block .rating-value {
      font-size: 0.9rem;
      font-weight: 600;
    }

    /* Mobile usefulness stars - reduced size */
    .mobile-usefulness-block .btn {
      min-height: 28px;
      min-width: 28px;
      padding: 0.1rem;
    }

    .mobile-usefulness-block .usefulness-label {
      font-size: 0.8rem;
      margin-bottom: 0 !important;
    }

    /* Optimize hub/community links for mobile */
    .hub-link,
    .community-link {
      font-size: 1rem;
      font-weight: 500;
    }

    /* Mobile author section - reduced size */
    .mobile-author-time {
      padding: 0;
    }

    .mobile-author-time .author-name {
      font-size: 0.9rem;
      font-weight: 500;
      line-height: 1.2;
    }

    .mobile-author-time .post-time {
      font-size: 0.8rem;
      line-height: 1.2;
    }

    .mobile-author-time .avatar {
      width: 24px !important;
      height: 24px !important;
    }

    /* Reduce spacing between mobile sections */
    .mobile-hub-row,
    .mobile-community-row {
      margin-bottom: 0.5rem !important;
    }
  }

  /* Touch-friendly interactions */
  @media (hover: none) and (pointer: coarse) {
    .rating-buttons .btn {
      min-height: 44px;
      min-width: 44px;
    }

    /* Remove hover effects on touch devices */
    .card:hover {
      transform: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .post-title-link-wrapper:hover {
      color: inherit;
    }

    .hub-link:hover,
    .community-link:hover {
      color: #495057;
      text-decoration: none !important;
    }

    /* Remove hover effects on rating buttons for touch devices */
    .rating-buttons .btn:hover {
      background-color: var(--bs-btn-bg) !important;
      border-color: var(--bs-btn-border-color) !important;
      color: var(--bs-btn-color) !important;
    }

    .rating-buttons .btn-outline-success:hover {
      background-color: transparent !important;
      border-color: #198754 !important;
      color: #198754 !important;
    }

    .rating-buttons .btn-outline-danger:hover {
      background-color: transparent !important;
      border-color: #dc3545 !important;
      color: #dc3545 !important;
    }

    .rating-buttons .btn-success:hover {
      background-color: #198754 !important;
      border-color: #198754 !important;
      color: #fff !important;
    }

    .rating-buttons .btn-danger:hover {
      background-color: #dc3545 !important;
      border-color: #dc3545 !important;
      color: #fff !important;
    }

    /* Remove focus outline on usefulness star buttons for touch devices */
    .btn:focus {
      box-shadow: none;
      outline: none;
    }

    /* Ensure star buttons don't show focus styles that could be misleading */
    .btn:focus:not(:focus-visible) {
      box-shadow: none;
      outline: none;
    }
  }

  /* Usefulness star styling */
  .rating-star.user-rated {
    position: relative;
  }

  .rating-star.user-rated::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    width: 100%;
    height: 2px;
    background-color: #ffc107;
    border-radius: 0;
  }

  /* Small mobile devices */
  @media (max-width: 576px) {
    .card-body {
      padding: 0.75rem !important;
    }

    .card-text,
    .post-content {
      font-size: 0.9rem;
    }

    .hub-image-container,
    .community-image-container {
      width: 20px;
      height: 20px;
    }
  }
</style>
